import { ethers } from "ethers";

// Network Configuration
const NETWORK_CONFIG = {
  name: "MEGA Testnet",
  chainId: 6342,
  networkId: 6342,
  rpcUrl: "https://carrot.megaeth.com/rpc",
  nativeToken: "ETH",
  decimals: 18,
  blockTime: 1000, // 1s for EVM blocks
  explorer: "https://megaexplorer.xyz",
};

// Contract Addresses
const CONTRACTS = {
  WETH: "******************************************",
  cUSD: "******************************************",
  ROUTER: "******************************************",
};

// Router ABI (simplified for swapping)
const ROUTER_ABI = [
  "function swapExactETHForTokens(uint amountOutMin, address[] calldata path, address to, uint deadline) external payable returns (uint[] memory amounts)",
  "function swapExactTokensForETH(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)",
  "function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)",
];

// ERC20 ABI (for approvals)
const ERC20_ABI = [
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function balanceOf(address owner) external view returns (uint256)",
  "function allowance(address owner, address spender) external view returns (uint256)",
];

const provider = new ethers.JsonRpcProvider(NETWORK_CONFIG.rpcUrl);

async function swapTokensForETH(tokenAmount, address, privateKey) {
  try {
      const wallet = new ethers.Wallet(privateKey, provider);
  const router = new ethers.Contract(CONTRACTS.ROUTER, ROUTER_ABI, wallet);
  const cUSDContract = new ethers.Contract(CONTRACTS.cUSD, ERC20_ABI, wallet);
  const amountIn = ethers.parseUnits(tokenAmount.toString(), 18);
  const path = [CONTRACTS.cUSD, CONTRACTS.WETH];
  const deadline = Math.floor(Date.now() / 1000) + 60 * 20; // 20 minutes

  console.log(`🔄 Menukar ${tokenAmount} cUSD menjadi ETH...`);

  // Periksa dan setujui jika diperlukan
  const allowance = await cUSDContract.allowance(address, CONTRACTS.ROUTER);
  if (allowance < amountIn) {
    console.log("🔑 Menyetujui pengeluaran cUSD...");
    const approveTx = await cUSDContract.approve(
      CONTRACTS.ROUTER,
      ethers.MaxUint256,
      { gasLimit: 100000 }
    );
    await approveTx.wait();
    console.log("✅ Persetujuan berhasil");
  }

  const tx = await router.swapExactTokensForETH(
    amountIn,
    0, // Accept any amount of ETH out
    path,
    address,
    deadline,
    { gasLimit: 500000 }
  );

  console.log(`📝 Hash transaksi: ${tx.hash}`);
  console.log(`🔗 Explorer: ${NETWORK_CONFIG.explorer}/tx/${tx.hash}`);

  const receipt = await tx.wait();
  console.log(`⛏️  Ditambang di blok: ${receipt.blockNumber}`);
  } catch (error) {
    console.log(error);

  }
}

async function swapETHForTokens(ethAmount, address, privateKey) {
  try {
      const wallet = new ethers.Wallet(privateKey, provider);
  const router = new ethers.Contract(CONTRACTS.ROUTER, ROUTER_ABI, wallet);
  const amountIn = ethers.parseEther(ethAmount.toString());
  const path = [CONTRACTS.WETH, CONTRACTS.cUSD];
  const deadline = Math.floor(Date.now() / 1000) + 60 * 20; // 20 minutes

  console.log(`🔄 Menukar ${ethAmount} ETH menjadi cUSD...`);

  const tx = await router.swapExactETHForTokens(
    0, // Terima jumlah token keluar berapa pun
    path,
    address,
    deadline,
    { value: amountIn, gasLimit: 500000 }
  );

  console.log(`📝 Hash transaksi: ${tx.hash}`);
  console.log(`🔗 Explorer: ${NETWORK_CONFIG.explorer}/tx/${tx.hash}`);

  const receipt = await tx.wait();
  console.log(`⛏️  Ditambang di blok: ${receipt.blockNumber}`);
  } catch (error) {
    console.log(error);

  }
}

export async function SwapDapp(address, privateKey, chuKy, number) {
  console.log("Swap dengan DApp");

  for (let i = 0; i < chuKy; i++) {
    try {
      console.log(`Sedang melakukan swap untuk dompet ${address}`);
      await swapETHForTokens(number, address, privateKey);
      await swapTokensForETH(number, address, privateKey);
    } catch (error) {
      console.log("Terjadi kesalahan saat melakukan swap");
    }
  }
}