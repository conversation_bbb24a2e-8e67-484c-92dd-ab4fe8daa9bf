import fs from "fs/promises";
import readline from "readline/promises";
import { SwapDapp } from "./swap.js";
import { swapDappGTE } from "./swap2.js";
import { mintTokenDappTeko } from "./mintToken.js";

const walletStr = await fs.readFile("wallets.txt", "utf-8");
const wallets = walletStr
  .trim()
  .split("\n")
  .map((a) => a.trim())
  .filter(a => a.length > 0); // Filter out empty lines

async function main() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  console.log("🚀 MegaETH Bot - CAPTCHA Free Version");
  console.log(`📝 Loaded ${wallets.length} wallets`);
  
  // Display wallet addresses
  wallets.forEach((wallet, index) => {
    const [address] = wallet.split("|");
    console.log(`   ${index + 1}. ${address}`);
  });

  const swap = await rl.question("\n🔄 Bạn có muốn swap không? (y/n): ");
  const mintToken = await rl.question("🪙 Bạn có muốn mintToken hằng ngày không? (y/n): ");

  let number;
  let chuKy;
  if (swap === "y") {
    number = await rl.question("💰 Số lượng muốn swap: ");
    chuKy = await rl.question("🔁 Số lần swap cho mỗi chu kỳ trong 1 ngày: ");
  }
  
  const runOnce = await rl.question("⚡ Chạy một lần hay lặp lại hàng ngày? (once/daily): ");
  
  rl.close();

  do {
    console.log("\n🎯 Bắt đầu chu kỳ mới...");
    
    for (let i = 0; i < wallets.length; i++) {
      const [address, privateKey] = wallets[i].split("|");
      console.log(`\n👛 Đang thực hiện ví ${i + 1}: ${address}`);
      
      try {
        if (swap === "y") {
          console.log("🔄 Bắt đầu swap...");
          await SwapDapp(address, privateKey, Number(chuKy), number);
          await swapDappGTE(number, privateKey, Number(chuKy));
          console.log("✅ Swap hoàn thành");
        }
        
        if (mintToken === "y") {
          console.log("🪙 Bắt đầu mint token...");
          await mintTokenDappTeko(privateKey);
          console.log("✅ Mint token hoàn thành");
        }
      } catch (error) {
        console.error(`❌ Lỗi với ví ${address}:`, error.message);
      }
    }
    
    if (runOnce === "daily") {
      console.log("\n⏰ Chờ 24 giờ để tiếp tục chu kỳ tiếp theo...");
      await new Promise((resolve) => setTimeout(resolve, 24 * 60 * 60 * 1000));
    }
  } while (runOnce === "daily");
  
  console.log("\n🎉 Bot đã hoàn thành!");
}

main().catch(console.error);
