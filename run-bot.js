import fs from "fs/promises";
import { SwapDapp } from "./swap.js";
import { swapDappGTE } from "./swap2.js";
import { mintTokenDappTeko } from "./mintToken.js";

const walletStr = await fs.readFile("wallets.txt", "utf-8");
const wallets = walletStr
  .trim()
  .split("\n")
  .map((a) => a.trim())
  .filter(a => a.length > 0);

async function runBot() {
  console.log("🚀 Bot MegaETH - Mode Otomatis");
  console.log(`📝 Berhasil memuat ${wallets.length} dompet`);

  // Tampilkan alamat dompet
  wallets.forEach((wallet, index) => {
    const [address] = wallet.split("|");
    console.log(`   ${index + 1}. ${address}`);
  });

  // Konfigurasi
  const enableSwap = true;
  const enableMintToken = true;
  const swapAmount = "0.001"; // Jumlah untuk swap
  const swapCycles = 1; // Jumlah siklus swap

  console.log("\n⚙️  Konfigurasi:");
  console.log(`   🔄 Swap: ${enableSwap ? 'Aktif' : 'Nonaktif'}`);
  console.log(`   🪙 Mint Token: ${enableMintToken ? 'Aktif' : 'Nonaktif'}`);
  if (enableSwap) {
    console.log(`   💰 Jumlah Swap: ${swapAmount} ETH`);
    console.log(`   🔁 Siklus Swap: ${swapCycles}`);
  }

  console.log("\n🎯 Memulai eksekusi bot...");

  for (let i = 0; i < wallets.length; i++) {
    const [address, privateKey] = wallets[i].split("|");
    console.log(`\n👛 Memproses dompet ${i + 1}: ${address}`);

    try {
      if (enableSwap) {
        console.log("🔄 Memulai operasi swap...");
        await SwapDapp(address, privateKey, swapCycles, swapAmount);
        await swapDappGTE(swapAmount, privateKey, swapCycles);
        console.log("✅ Operasi swap selesai");
      }

      if (enableMintToken) {
        console.log("🪙 Memulai mint token...");
        await mintTokenDappTeko(privateKey);
        console.log("✅ Mint token selesai");
      }
    } catch (error) {
      console.error(`❌ Terjadi kesalahan pada dompet ${address}:`, error.message);
    }

    // Tambahkan jeda antar dompet
    if (i < wallets.length - 1) {
      console.log("⏳ Menunggu 5 detik sebelum dompet berikutnya...");
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }

  console.log("\n🎉 Eksekusi bot selesai!");
}

runBot().catch(console.error);
