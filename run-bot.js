import fs from "fs/promises";
import { SwapDapp } from "./swap.js";
import { swapDappGTE } from "./swap2.js";
import { mintTokenDappTeko } from "./mintToken.js";

const walletStr = await fs.readFile("wallets.txt", "utf-8");
const wallets = walletStr
  .trim()
  .split("\n")
  .map((a) => a.trim())
  .filter(a => a.length > 0);

async function runBot() {
  console.log("🚀 MegaETH Bot - Auto Mode");
  console.log(`📝 Loaded ${wallets.length} wallets`);
  
  // Display wallet addresses
  wallets.forEach((wallet, index) => {
    const [address] = wallet.split("|");
    console.log(`   ${index + 1}. ${address}`);
  });

  // Configuration
  const enableSwap = true;
  const enableMintToken = true;
  const swapAmount = "0.001"; // Amount to swap
  const swapCycles = 1; // Number of swap cycles

  console.log("\n⚙️  Configuration:");
  console.log(`   🔄 Swap: ${enableSwap ? 'Enabled' : 'Disabled'}`);
  console.log(`   🪙 Mint Token: ${enableMintToken ? 'Enabled' : 'Disabled'}`);
  if (enableSwap) {
    console.log(`   💰 Swap Amount: ${swapAmount} ETH`);
    console.log(`   🔁 Swap Cycles: ${swapCycles}`);
  }

  console.log("\n🎯 Starting bot execution...");
  
  for (let i = 0; i < wallets.length; i++) {
    const [address, privateKey] = wallets[i].split("|");
    console.log(`\n👛 Processing wallet ${i + 1}: ${address}`);
    
    try {
      if (enableSwap) {
        console.log("🔄 Starting swap operations...");
        await SwapDapp(address, privateKey, swapCycles, swapAmount);
        await swapDappGTE(swapAmount, privateKey, swapCycles);
        console.log("✅ Swap operations completed");
      }
      
      if (enableMintToken) {
        console.log("🪙 Starting token minting...");
        await mintTokenDappTeko(privateKey);
        console.log("✅ Token minting completed");
      }
    } catch (error) {
      console.error(`❌ Error with wallet ${address}:`, error.message);
    }
    
    // Add delay between wallets
    if (i < wallets.length - 1) {
      console.log("⏳ Waiting 5 seconds before next wallet...");
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  console.log("\n🎉 Bot execution completed!");
}

runBot().catch(console.error);
