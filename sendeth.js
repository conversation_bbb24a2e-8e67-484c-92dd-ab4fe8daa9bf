import { ethers } from "ethers";

const provider = new ethers.JsonRpcProvider("https://carrot.megaeth.com/rpc");
const wallet = new ethers.Wallet(
  "privateKey",
  provider
);

const addresses = [
"******************************************",
"******************************************",
/.../
];

const amountToSend = ethers.parseEther("0.03");

async function sendETH() {
  for (const address of addresses) {
    try {
      const tx = await wallet.sendTransaction({
        to: address,
        value: amountToSend,
      });

      console.log(`✅ Gửi thành công đến ${address} | TX: ${tx.hash}`);
      await tx.wait();
    } catch (error) {
      console.error(`❌ Lỗi khi gửi đến ${address}:`, error);
    }
  }
}

sendETH();
