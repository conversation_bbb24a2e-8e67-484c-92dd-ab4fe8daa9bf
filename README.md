 # BOT OTOMATIS SWAP DAN MINT TOKEN DI MEGAETH CHAIN


 ## 1. Fitur
- Mint Token (4 kontrak berbeda)
- Swap ETH ↔ cUSD (2 DApp)
- Tanpa CAPTCHA (faucet dihapus)
- Interface dalam Bahasa Indonesia

 ## 2. <PERSON> men<PERSON> bot
 - Clone source code
   ```git clone https://github.com/hthodev/megaETH.git```
 - Install dependencies
   ```npm install```
 - Masukkan alamat dan private key di file wallets.txt
   **Format: alamat|privateKey**

 ## 3. Perintah NPM
 - Jalankan bot interaktif (dengan input pengguna)
   ```npm start```
 - Jalan<PERSON> bot otomatis (tanpa input)
   ```npm run auto```
 - Mint NFT
   ```npm run mint-nft```

 ## 4. Perintah Node.js (alternatif)
 - Bot interaktif: ```node main.js```
 - Bot otomatis: ```node run-bot.js```
 - Mint NFT: ```node mintnft.js```
