 # TOOL AUTOMATION SWAP, MINT NFT, MINT TOKEN ON MEGAETH CHAIN


 ## 1. Function
- Mint NFT
- Mint Token
- Swap (2DApp)
- Faucet

 ## 2. How to run code
 - Clone source
   ```git clone https://github.com/hthodev/megaETH.git```
 - Install package
   ```npm install```
 - Input your address and private key on file wallets.txt
   **address|privateKey**
 - Input proxy if you use file faucet. To faucet, you need API key to resolve captcha
 - Run code mint nft
   ```node mintnft.js```
 - Run code swap and mint token
   ```node main.js```
