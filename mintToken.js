import { ethers } from "ethers";
const RPC_URL = "https://carrot.megaeth.com/rpc";

const ABI = [
  {
    "inputs": [
      {
        "internalType": "address",
        "name": "to",
        "type": "address"
      },
      {
        "internalType": "uint256",
        "name": "amount",
        "type": "uint256"
      }
    ],
    "name": "mint",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

export async function mintTokenDappTeko(privateKey) {
  console.log("Swap với <PERSON>");

  const contracts_address = [
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
  ];
  const provider = new ethers.JsonRpcProvider(RPC_URL);
  const wallet = new ethers.Wallet(privateKey, provider);

  for (const ca of contracts_address) {
    const contract = new ethers.Contract(ca, ABI, wallet);
    const tx = await contract.mint("******************************************", ethers.parseEther("0.00"));

    console.log("Minting... Tx hash:", tx.hash);
    await tx.wait();
    console.log("✅ Minted token successfully!");
  }
}