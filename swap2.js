import { ethers } from "ethers";

const RPC_URL = "https://carrot.megaeth.com/rpc";
const provider = new ethers.JsonRpcProvider(RPC_URL);
const ROUTER_ADDRESS = "******************************************";

const amountOutMin = BigInt("43075991243938");
const path = [
  "******************************************",
  "******************************************",
];
const to = "******************************************";
const deadline = 1748800162796;

async function swapETHForTokens(ethAmountIn, wallet) {
  ethAmountIn = ethers.parseEther(ethAmountIn.toString());
  console.log("Sedang menukar ETH menjadi USD");

  const ROUTER_ABI_ETH_TO_TOKENS = [
    {
      inputs: [
        { internalType: "uint256", name: "amountOutMin", type: "uint256" },
        { internalType: "address[]", name: "path", type: "address[]" },
        { internalType: "address", name: "to", type: "address" },
        { internalType: "uint256", name: "deadline", type: "uint256" },
      ],
      name: "swapExactETHForTokens",
      outputs: [
        { internalType: "uint256[]", name: "amounts", type: "uint256[]" },
      ],
      stateMutability: "payable",
      type: "function",
    },
  ];
  const router = new ethers.Contract(
    ROUTER_ADDRESS,
    ROUTER_ABI_ETH_TO_TOKENS,
    wallet
  );

  const tx = await router.swapExactETHForTokens(
    amountOutMin,
    path,
    to,
    deadline,
    {
      value: ethAmountIn,
      gasLimit: 300000,
    }
  );

  console.log("⏳ Mengirim transaksi:", tx.hash);
  const receipt = await tx.wait();
  console.log("✅ Berhasil! Transaksi telah dikonfirmasi:", receipt.hash);
}

async function swapTokensForETH(wallet) {
  console.log("Sedang menukar semua USD menjadi ETH");

  const ABI_TOKEN_TO_ETH = [
    {
      name: "swapExactTokensForETH",
      type: "function",
      stateMutability: "nonpayable",
      inputs: [
        {
          name: "amountIn",
          type: "uint256",
        },
        {
          name: "amountOutMin",
          type: "uint256",
        },
        {
          name: "path",
          type: "address[]",
        },
        {
          name: "to",
          type: "address",
        },
        {
          name: "deadline",
          type: "uint256",
        },
      ],
      outputs: [
        {
          name: "amounts",
          type: "uint256[]",
        },
      ],
    },
  ];
  const usdToken = new ethers.Contract(
    "******************************************",
    [
      "function balanceOf(address owner) view returns (uint256)",
      "function approve(address spender, uint256 amount) public returns (bool)",
    ],
    wallet
  );
  const routerAddress = "******************************************";
  const router = new ethers.Contract(routerAddress, ABI_TOKEN_TO_ETH, wallet);

  const balance = await usdToken.balanceOf(wallet.address);
  if (balance === 0n) {
    console.log("❌ Dompet tidak memiliki token USD untuk ditukar.");
    return;
  }

  const amountIn = balance;
  const amountOutMin = 0n;
  const path = [
    "******************************************",
    "******************************************",
  ];
  const to = wallet.address;
  const deadline = BigInt("1748801344561");

  // Setujui token USD
  const approveTx = await usdToken.approve(routerAddress, amountIn);
  await approveTx.wait();

  // Kirim transaksi swap
  const tx = await router.swapExactTokensForETH(
    amountIn,
    amountOutMin,
    path,
    to,
    deadline,
    { gasLimit: 300000 }
  );

  console.log("Hash transaksi swap:", tx.hash);
  await tx.wait();
  console.log("✅ Swap selesai!");
}

export async function swapDappGTE(ethAmountIn, PRIVATE_KEY, chuKy) {
  console.log("Swap dengan DApp GTE");
  for (let i = 0; i < chuKy; i++) {
    try {
      const wallet = new ethers.Wallet(PRIVATE_KEY, provider);
      await swapETHForTokens(ethAmountIn, wallet);
      await swapTokensForETH(wallet);
    } catch (error) {
      console.error("❌ Kesalahan saat melakukan swap:", error);
    }
  }
}
