import fs from "fs/promises";
import readline from "readline/promises";
import { SwapDapp } from "./swap.js";
import { swapDappGTE } from "./swap2.js";
import { mintTokenDappTeko } from "./mintToken.js";

const walletStr = await fs.readFile("wallets.txt", "utf-8");
const wallets = walletStr
  .trim()
  .split("\n")
  .map((a) => a.trim())
  .filter(a => a.length > 0); // Filter out empty lines

async function main() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  console.log("🚀 Bot MegaETH - Versi Tanpa CAPTCHA");
  console.log(`📝 Berhasil memuat ${wallets.length} dompet`);

  // Tampilkan alamat dompet
  wallets.forEach((wallet, index) => {
    const [address] = wallet.split("|");
    console.log(`   ${index + 1}. ${address}`);
  });

  const swap = await rl.question("\n🔄 Apakah Anda ingin melakukan swap? (y/n): ");
  const mintToken = await rl.question("🪙 Apakah Anda ingin mint token setiap hari? (y/n): ");

  let number;
  let chuKy;
  if (swap === "y") {
    number = await rl.question("💰 Jumlah yang ingin di-swap: ");
    chuKy = await rl.question("🔁 Jumlah siklus swap per hari: ");
  }

  const runOnce = await rl.question("⚡ Jalankan sekali atau berulang setiap hari? (sekali/harian): ");

  rl.close();

  do {
    console.log("\n🎯 Memulai siklus baru...");

    for (let i = 0; i < wallets.length; i++) {
      const [address, privateKey] = wallets[i].split("|");
      console.log(`\n👛 Memproses dompet ${i + 1}: ${address}`);

      try {
        if (swap === "y") {
          console.log("🔄 Memulai operasi swap...");
          await SwapDapp(address, privateKey, Number(chuKy), number);
          await swapDappGTE(number, privateKey, Number(chuKy));
          console.log("✅ Operasi swap selesai");
        }

        if (mintToken === "y") {
          console.log("🪙 Memulai mint token...");
          await mintTokenDappTeko(privateKey);
          console.log("✅ Mint token selesai");
        }
      } catch (error) {
        console.error(`❌ Terjadi kesalahan pada dompet ${address}:`, error.message);
      }
    }

    if (runOnce === "harian") {
      console.log("\n⏰ Menunggu 24 jam untuk siklus berikutnya...");
      await new Promise((resolve) => setTimeout(resolve, 24 * 60 * 60 * 1000));
    }
  } while (runOnce === "harian");

  console.log("\n🎉 Bot telah selesai dijalankan!");
}

main().catch(console.error);
